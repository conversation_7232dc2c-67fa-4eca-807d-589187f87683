import { requestService } from '../services/req.ts';
import type { PagedQueryParam, PermissionBaseRes } from './_common.ts';

export interface DepotsListItem {
  ID: number;
  prefix: string;
  depth?: number;
  isLocal?: boolean;
  serverID?: number;
  name?: string;
}
export interface StreamsListItem {
  ID?: number;
  CreatedAt?: string;
  UpdatedAt?: string;
  depotID?: number;
  name?: string;
  path?: string;
  description?: string;
  streamType?: number;
  rootDirectory?: string;
  workspace?: string;
  step?: number;
  userID?: number;
  sort?: number;
  submittingCount?: number;
  todaySubmittedCount?: number;
  submitStreamID?: number;
  instances?: {
    compile: boolean;
    status: number;
  }[];
  instanceCount?: number;
}
export interface ListSubmitItem {
  description: string;
  isLocal: boolean;
  name: string;
  prefix: string;
  projectID: number;
  repoType: number;
  sort: number;
  streams: StreamsListItem[];
}

export interface RecordListItem {
  ID: number;
  shelveCL: number;
  reviewCL: number;
  submitCL: number;
  serverID: number;
  streamID: number;
  swarmBaseURL: string;
  submitterName: string;
  submitter: Submitter;
  hasResCheck: boolean;
  submitState: number;
  reviewState: number;
  checkState: number;
}
export interface Submitter {

  ID?: number;
  CreatedAt?: string;
  UpdatedAt?: string;
  uuid?: string;
  userName?: string;
  nickName?: string;
  sideMode?: string;
  headerImg?: string;
  baseColor?: string;
  activeColor?: string;
  authorityId?: number;
  authority?: {
    CreatedAt?: string;
    UpdatedAt?: string;
    DeletedAt?: null;
    authorityId?: number;
    authorityName?: string;
    parentId?: number;
    dataAuthorityId?: null;
    children?: null;
    menus?: null;
    defaultRouter?: string;
  };
  authorities?: null;
  phone?: string;
  email?: string;
  enable?: number;
  authType?: number;
  openID?: string;
}
export interface SubmitInfo {
  changelist?: number;
  submitterName?: string;
  submitter?: Submitter;
  date?: string;
  description?: string;
}
export interface QaCheckInfo {
  recordID?: number;
  shelveCL?: number;
  checkQaList: Submitter[];
  submitterName?: string;
  submitter?: Submitter;
  date?: string;
  description?: string;
  workItemID?: number;
  workItemTitle?: string;
  workItemURL?: string;
  canApply?: boolean;
  checkTime?: string;
}

export interface ExampleConfigItem {
  id: number;
  ipAddress?: string;
  instanceType?: number;
  workState?: number;
  pendingState?: number;
  disabled?: boolean;
  name?: string;
  workspace?: string;
  bootTimestamp?: number;
}

export interface OperationsListItem {
  checkInstance: ExampleConfigItem;
  operator: Submitter;
  execTime: string;
  finishTime: string;
  operationType: number;
  modifications: {
    modifyKey: string;
    oldValue: string;
    newValue: string;
  }[];

}
export interface scheduleItem {
  id: number;
  operationType: number;
  triggerType: number;
  triggerTimestamp: number;
  triggerCron: string;
  instanceIDs: number[];
}

export interface DistributeItem {
  distributeType: number;
  ruleList: {
    instanceType: number;
    suffixList: string[];
  }[];
}
/**
 * 获取指定分支信息
 */
export const getStreamsInfo = requestService.GET<
  { id: number; stream_id: number },
  Record<string, never>,
  PermissionBaseRes<{ stream: StreamsListItem }>
>('/api/v1/projects/:id/submit/streams/:stream_id/info');

/**
 * 获取主分支列表页信息
 */
export const getListSubmit = requestService.GET<
  { id: number ; projectCode: string },
  Record<string, never>,
  PermissionBaseRes<{ list: ListSubmitItem[] }>
>('/api/v1/projects/:id/submit/streams/listSubmit');

/**
 * 查询 CommitServer 上的 stream 记录
 */
export const getRecordListByPage = requestService.GET<
  { id: number; streamID: number; submitState: number; keyword: string } & PagedQueryParam,
  Record<string, never>,
  PermissionBaseRes< { list: RecordListItem[]; total: number }>
>('/api/v1/projects/:id/submit/changeLists/recordList');

/**
 * 获取提交详情
 */
export const getSubmitInfoApi = requestService.GET<
  { id: number; recordID: number },
  Record<string, never>,
  PermissionBaseRes<SubmitInfo>
>('/api/v1/projects/:id/submit/changeLists/submitInfo');

/**
 * 获取审批详情
 */
export const getQaCheckInfoApi = requestService.GET<
  { id: number; recordID: number },
  Record<string, never>,
  PermissionBaseRes<QaCheckInfo>
>('/api/v1/projects/:id/submit/changeLists/qaCheckInfo');

/**
 *执行审批
 */
export const applyQaCheckApi = requestService.POST<
  { id: number },
  { clRecordID: number;ApproveCode: number },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/changeLists/applyQaCheck');

/**
 *实例配置页列表
 */
export const getExampleConfigListApi = requestService.GET<
  { id: number } & { streamID: number },
  Record<string, never>,
  PermissionBaseRes<ExampleConfigItem[]>
>('/api/v1/projects/:id/submit/checkInstances/configList');

/**
 *实例配置页排序
 */
export const batchSortListApi = requestService.POST<
  { id: number },
  { streamID: number; idList: number[] },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/checkInstances/batchSort');

/**
 *实例配置页编辑
 */
export const updateExampleConfigItemApi = requestService.POST<
  { id: number },
  { streamID: number;instanceID: number; name: string },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/checkInstances/update');

/**
 *实例配置更新/重启/禁用实例
 */
export const updateExampleOperateApi = requestService.POST<
  { id: number },
  { streamID: number;instanceID: number; operation: number },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/checkInstances/operate');

/**
 *全部更新/重启实例
 */
export const updateExamplebatchOperateApi = requestService.POST<
  { id: number },
  { streamID: number; operation: number },
  PermissionBaseRes<undefined>
>('/api/v1/projects/:id/submit/checkInstances/batchOperate');

/**
 *全部更新/重启实例
 */
export const getOperationsListApi = requestService.GET<
  { id: number; streamID: number; page: number; pageSize: number;operatorNames?: string[]; startTime?: number; endTime?: number;operationTypes?: number[] },
  Record<string, never>,
  PermissionBaseRes< { list: OperationsListItem[]; total: number }>
>('/api/v1/projects/:id/submit/checkInstances/operations');

/**
 *查询实例分配规则
 */
export const getDistributeApi = requestService.GET<
  { id: number; streamID: number },
  Record<string, never>,
  PermissionBaseRes<DistributeItem>
>('/api/v1/projects/:id/submit/instanceRules/distribute');

/**
 *更新实例分配规则
 */
export const setDistributeApi = requestService.POST<
  { id: number },
  DistributeItem & {
    streamID: number;
  },
  PermissionBaseRes<null>
>('/api/v1/projects/:id/submit/instanceRules/distribute');

/**
 *查询实例定时任务列表
 */
export const getScheduleListApi = requestService.GET<
  { id: number ;streamID: number },
  Record<string, never>,
  PermissionBaseRes<scheduleItem[]>
>('/api/v1/projects/:id/submit/instanceRules/scheduleList');
