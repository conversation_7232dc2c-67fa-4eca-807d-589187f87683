<template>
  <div>
    <Form :labelCol="labelCol" :wrapperCol="wrapperCol">
      <FormItem label="操作类型" v-bind="validateInfos.operationType">
        <Input v-model:value="modelRef.operationType" />
      </FormItem>
      <FormItem label="触发类型" v-bind="validateInfos.triggerType">
        <RadioGroup v-model:value="modelRef.triggerType">
          <Radio :value="1">
            触发单次
          </Radio>
          <Radio :value="2">
            周期触发
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem v-if="modelRef.triggerType === 1" label="触发时间" v-bind="validateInfos.triggerTimestamp">
        <DatePicker v-model:value="modelRef.triggerTimestamp" showTime />
      </FormItem>
      <FormItem v-if="modelRef.triggerType === 2" label="触发时间" v-bind="validateInfos.name">
       <a-time-picker
        v-model:value="formState['time-picker']"
        format="HH:mm:ss"
        value-format="HH:mm:ss"
      />
      </FormItem>
      <FormItem v-if="modelRef.triggerType === 2" v-bind="validateInfos.name">
        <Input v-model:value="modelRef.name" />
      </FormItem>
      <FormItem label="执行实例" v-bind="validateInfos.name">
        <Input v-model:value="modelRef.name" />
      </FormItem>
    </Form>
  </div>
</template>

<script lang="ts" setup>
import { DatePicker, Form, FormItem, Input, Radio, RadioGroup } from 'ant-design-vue';
import { reactive } from 'vue';

const labelCol = { span: 4 };
const wrapperCol = { span: 14 };
const useForm = Form.useForm;
const modelRef = reactive({
  name: '',
  operationType: 1,
  triggerType: 1,
  triggerTimestamp: undefined,
});
const rulesRef = reactive({
  name: [
    {
      required: true,
      message: 'Please input name',
    },
  ],
});
const { validate, validateInfos } = useForm(modelRef, rulesRef);
</script>
