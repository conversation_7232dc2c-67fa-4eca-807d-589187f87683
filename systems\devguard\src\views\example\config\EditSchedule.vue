<template>
  <div>
    <Form :labelCol="labelCol" :wrapperCol="wrapperCol">
      <FormItem label="操作类型" v-bind="validateInfos.operationType">
        <Select v-model:value="modelRef.operationType" :options="scheduleOperationTypeOptions" />
      </FormItem>
      <FormItem label="触发类型" v-bind="validateInfos.triggerType">
        <RadioGroup v-model:value="modelRef.triggerType">
          <Radio :value="1">
            触发单次
          </Radio>
          <Radio :value="2">
            周期触发
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem v-if="modelRef.triggerType === 1" label="触发时间" v-bind="validateInfos.triggerTimestamp">
        <DatePicker v-model:value="modelRef.triggerTimestamp" showTime />
      </FormItem>
      <FormItem v-if="modelRef.triggerType === 2" label="触发时间" v-bind="validateInfos.triggerTime">
        <TimePicker
          v-model:value="modelRef.triggerTime"
        />
      </FormItem>
      <FormItem v-if="modelRef.triggerType === 2" v-bind="validateInfos.triggerWeekDay">
        <CheckboxGroup v-model:value="modelRef.triggerWeekDay" class="w-full flex">
          <Checkbox :value="1">
            周一
          </Checkbox>
          <Checkbox :value="2">
            周二
          </Checkbox>
          <Checkbox :value="3">
            周三
          </Checkbox>
          <Checkbox :value="4">
            周四
          </Checkbox>
          <Checkbox :value="5">
            周五
          </Checkbox>
          <Checkbox :value="6">
            周六
          </Checkbox>
          <Checkbox :value="7">
            周日
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="执行实例" v-bind="validateInfos.instanceIDs">
        <Select v-model:value="modelRef.instanceIDs" mode="multiple" :options="exampleConfig" :fieldNames="{ label: 'name', value: 'id' }" />
      </FormItem>
    </Form>
    
  </div>
</template>

<script lang="ts" setup>
import { Checkbox, DatePicker, Form, FormItem, Radio, RadioGroup, Select, TimePicker } from 'ant-design-vue';
import { reactive } from 'vue';
import { scheduleOperationTypeOptions } from './type.data';
import type { ExampleConfigItem } from '../../../api';

defineProps<{
  exampleConfig: ExampleConfigItem[];
}>();

const labelCol = { span: 4 };
const wrapperCol = { span: 20 };
const useForm = Form.useForm;
const modelRef = reactive({
  name: '',
  operationType: 1,
  triggerType: 1,
  triggerTimestamp: undefined,
  triggerTime: undefined,
  triggerWeekDay: [],
  instanceIDs: [],
});
const rulesRef = reactive({
  operationType: [
    {
      required: true,
      message: '请选择操作类型',
    },
  ],
  triggerType: [
    {
      required: true,
      message: '请选择触发类型',
    },
  ],
  triggerTimestamp: [
    {
      required: true,
      message: '请选择触发时间',
    },
  ],
  triggerTime: [
    {
      required: true,
      message: '请选择触发时间',
    },
  ],
  triggerWeekDay: [
    {
      required: true,
      message: '请选择触发时间',
    },
  ],
  instanceIDs: [
    {
      required: true,
      message: '请选择执行实例',
    },
  ],

});
const { validate, validateInfos } = useForm(modelRef, rulesRef);
async function submit() {
  await validate();
  console.log('modelRef', modelRef);
}
</script>
